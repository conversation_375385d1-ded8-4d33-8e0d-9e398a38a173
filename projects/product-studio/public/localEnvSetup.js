// localEnvSetup.js for Product Studio
(function () {
  const isLocal = window.location.hostname === 'localhost';
  if (isLocal) {
    window.env = {
      baseUrl: 'https://aava-dev.avateam.io',
      production: 'false',
      productStudioUrl: 'http://localhost:4202/product/',
      productApiUrl: '/server/product',
      pipelineApiBaseUrl: 'https://aava-dev.avateam.io/server/product',
      consoleApiAuthUrl: 'https://aava-dev.avateam.io/api/auth',
      productStudioRedirectUrl: 'http://localhost:4202/product/',
      elderWandUrl: 'http://localhost:4200',
      experienceStudioUrl: 'http://localhost:4201/experience/',
      consoleUrl: 'http://localhost:4203/console',
    };
  }
})();
