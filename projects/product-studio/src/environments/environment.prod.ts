// Environment configuration for Product Studio Application - Production

// Helper function to safely get environment variables from window.env
const getRequiredEnv = (key: string): string => {
  interface EnvWindow extends Window {
    env?: Record<string, string>;
  }
  const envWindow = window as EnvWindow;
  const value = envWindow.env?.[key];
  if (value === undefined || value === null) {
    throw new Error(
      `Environment variable '${key}' is not defined in window.env.`
    );
  }
  return String(value);
};

export const environment = {
  production: true,

  // API Configuration
  // API_BASE_URL will be injected from Docker environment, fallback to production default
  apiBaseUrl: getRequiredEnv('baseUrl'),
  // apiUrl:getRequiredEnv('baseUrl'),

  // Pipeline API Configuration
  pipelineApiBaseUrl: getRequiredEnv('productApiUrl'), //todo change it into correct pipeline API URL after deployment

  // Authentication Configuration
  productStudioApiAuthUrl: getRequiredEnv('consoleApiAuthUrl'),
  productStudioRedirectUrl: getRequiredEnv('consoleRedirectUrl'),

  // Studio app URLs
  // elderWandUrl:
  //   (window as any).__env?.elderWandUrl ||
  //   'https://aava-dev.avateam.io/launchpad/',
  experienceStudioUrl: getRequiredEnv('experienceStudioUrl'),
  productStudioUrl: getRequiredEnv('productApiUrl'),

  // consoleUrl:
  //   (window as any).__env?.consoleUrl || 'https://aava-dev.avateam.io/console/',
  elderWandUrl: getRequiredEnv('elderWandUrl'),
  apiUrl: getRequiredEnv('baseUrl'),
  experienceApiUrl: getRequiredEnv('experienceApiUrl'),
  experianceApiAuthUrl: getRequiredEnv('consoleApiAuthUrl'),
  experianceRedirectUrl: getRequiredEnv('experienceStudioUrl'),
  // Studio app URLs
  consoleUrl: getRequiredEnv('consoleUrl'),
  // Helper function to get API endpoint with base URL
  getApiUrl: (endpoint: string) => {
    const baseUrl = getRequiredEnv('baseUrl');
    return `${baseUrl}${endpoint}`;
  },
};
