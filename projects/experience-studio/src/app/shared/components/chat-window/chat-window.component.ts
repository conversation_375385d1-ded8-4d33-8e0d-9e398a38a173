import { CommonModule } from '@angular/common';
import {
  AfterViewChecked,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
  ViewEncapsulation,
  DestroyRef,
  inject,
  signal,
  computed,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, fromEvent, throttleTime } from 'rxjs';
import { CardsComponent, PromptBarComponent } from '@awe/play-comp-library';
import { IconsComponent } from '../icons/icons.component';
import { MarkdownModule } from 'ngx-markdown';
import { VerticalStepperComponent } from '../vertical-stepper/vertical-stepper.component';

import { GenerationAccordionComponent, GenerationResult } from '../generation-accordion/generation-accordion.component';
import { IntroMessageShimmerComponent } from '../ai-card-shimmer/ai-card-shimmer.component';
import { StepperStateService } from '../../services/stepper-state.service';
import { RegenerationIntegrationService } from '../../services/regeneration-integration.service';
import { SequentialRegenerationService } from '../../services/sequential-regeneration.service';
import { EnhancedSSEService } from '../../services/enhanced-sse.service';
import {createLogger} from '../../utils/logger';
import { ContentSanitizationService } from '../../services/content-sanitization.service';
import { PromptBarService } from '../../services/prompt-bar-services/prompt-bar.service';
import { ToastService } from '../../services/toast.service';
import { CodeGenerationIntroService } from '../../services/code-generation-intro.service';
import { UIDesignIntroService } from '../../services/ui-design-intro.service';
import { WireframeMockDataService } from '../../services/wireframe-mock-data.service';

// Interface for selected files (same as prompt-content)
interface SelectedFile {
  id: string;
  name: string;
  url: string;
  type: string;
}

// ENHANCEMENT: Prompt bar loading state interface
export interface PromptBarLoadingState {
  isVisible: boolean;
  phase: 'CODE_GENERATION' | 'BUILD' | 'DEPLOY' | null;
  status: 'IN_PROGRESS' | 'COMPLETED' | null;
  loadingText: string;
  isInitialGeneration: boolean;
  isRegeneration: boolean;
}

// ENHANCEMENT: Smart scroll navigation state interface
// export interface ScrollNavigationState {
//   isAtTop: boolean;
//   isAtBottom: boolean;
//   showScrollButton: boolean;
//   scrollDirection: 'up' | 'down';
//   canScroll: boolean;
// }

@Component({
  selector: 'app-chat-window',
  standalone: true,
  imports: [CommonModule, PromptBarComponent, CardsComponent, MarkdownModule, VerticalStepperComponent, IconsComponent, GenerationAccordionComponent, IntroMessageShimmerComponent],
  templateUrl: './chat-window.component.html',
  styleUrls: ['./chat-window.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class ChatWindowComponent implements AfterViewChecked, OnInit, OnDestroy {
  // Angular 19+ inject pattern
  private readonly destroyRef = inject(DestroyRef);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly stepperStateService = inject(StepperStateService);
  private readonly contentSanitizationService = inject(ContentSanitizationService);
  private readonly promptService = inject(PromptBarService);
  private readonly toastService = inject(ToastService);
  private readonly regenerationIntegrationService = inject(RegenerationIntegrationService);
  private readonly sequentialRegenerationService = inject(SequentialRegenerationService);
  private readonly enhancedSSEService = inject(EnhancedSSEService);

  // Auto-scroll functionality removed
  private _showStepper: boolean = false;
  private logger = createLogger("ChatWindowComponent");

  // Stepper related properties
  showPreview: boolean = false;
  previewImage: { url: string, name: string } | null = null;
  private timeoutRefs: { [key: string]: any } = {};

  // Wireframe generation stepper properties
  private wireframeStepperVisible = signal(false);
  private wireframeCurrentStep = signal('');
  private wireframeStepperMessageId = signal<string | null>(null);

  // Reference to the stepper component
  @ViewChild(VerticalStepperComponent) private stepperComponent?: VerticalStepperComponent;
  @Input()
  get showStepper(): boolean {
    return this._showStepper;
  }
  set showStepper(value: boolean) {
    if (this._showStepper !== value) {
      this._showStepper = value;
      // Manage stepper message in chat flow
      if (value) {
        this.addStepperMessage();
        // If stepper is shown and we have progress, update the stepper message
        if (this.progress) {
          this.updateStepperMessage();
        }
      } else {
        // CRITICAL FIX: Don't remove stepper message during regeneration
        // The stepper should remain in chat history during code-regen events
        const isRegenerationActive = this.stepperStateService.isRegenerationActive();
        if (!isRegenerationActive) {
          this.removeStepperMessage();
        } else {
          this.logger.info('🔒 Preserving stepper in chat during regeneration - not removing stepper message');
        }
      }
    }
  }

  private _progress: string = '';
  @Input()
  get progress(): string {
    return this._progress;
  }
  set progress(value: string) {
    if (this._progress !== value) {
      this._progress = value;
      // Update stepper message when progress changes
      if (this.showStepper && value) {
        this.updateStepperMessage();
        // Auto-scroll removed - let user control scrolling
      }
    }
  }

  private _progressDescription: string = '';
  @Input()
  get progressDescription(): string {
    return this._progressDescription;
  }
  set progressDescription(value: string) {
    if (this._progressDescription !== value) {
      this._progressDescription = value;
      // Update stepper message when progress description changes
      if (this.showStepper && value) {
        this.updateStepperMessage();
        // Auto-scroll removed - let user control scrolling
      }
    }
  }

  @Input() status: string = 'PENDING';

  private _selectedImageDataUri: string = '';
  @Input()
  get selectedImageDataUri(): string {
    return this._selectedImageDataUri;
  }
  set selectedImageDataUri(value: string) {
    if (this._selectedImageDataUri !== value) {
      this._selectedImageDataUri = value;
      // If we have a new image, update the chat messages to include it
      if (value) {
        this.addImageToLastUserMessage();
      }
      // OnPush: Input property changes automatically trigger change detection
    }
  }

  @Input() defaultText: string = 'Ask me ...';
  @Input() leftIcons: { name: string; status: 'active' | 'default' | 'disable' }[] = [];
  @Input() rightIcons: { name: string; status: 'active' | 'default' | 'disable' }[] = [];
  @Input() isCodeGenerationComplete: boolean = false;
  @Input() isUIDesignLoading: boolean = false;
  @Input() isCodeGenerationLoading: boolean = false;
  @Input() isUIDesignMode: boolean = false;
  @Input() codeRegenerationProgressDescription: string = '';
  @Input() isProjectLoadingMode: boolean = false;


  // API integration inputs for vertical stepper
  @Input() projectId: string = '';
  @Input() jobId: string = '';
  @Input() useApi: boolean = false;

  // File upload state management (exact same as prompt-content)
  selectedFile: File | null = null;
  selectedFileName = '';
  selectedFiles: SelectedFile[] = [];
  isFileAttachDisabled = false;
  fileError = '';
  readonly maxAllowedFiles = 1;

  // Enhance prompt state management (exact same as prompt-content)
  isEnhancing = false;
  enhanceClickCount = 0;
  maxEnhanceClicks = 2;
  isPromptEnhanced = false;
  isEnhancedPromptValid = true;

  // Current prompt for enhancement (exact same as prompt-content)
  currentPrompt = '';

  // Submission data for regeneration payload
  submissionData: {
    prompt: string;
    timestamp: string;
    imageFile: File | null;
    imageUrl: string | null;
    imageDataUri: string | null;
    fileName: string | null;
  } = {
    prompt: '',
    timestamp: new Date().toISOString(),
    imageFile: null,
    imageUrl: null,
    imageDataUri: null,
    fileName: null,
  };




  private _theme: 'light' | 'dark' = 'light';
  @Input()
  get theme(): 'light' | 'dark' {
    return this._theme;
  }
  set theme(value: 'light' | 'dark') {
    this._theme = value;
    this.updateMessagesTheme();
    // Set flag to update card themes in next view check cycle
    this.shouldUpdateCardThemes = true;
    // Force update the theme on all cards when theme changes
    setTimeout(() => {
      this.forceUpdateCardThemes();
    }, 0);
  }

  private _chatMessages: {
    id?: string;
    text: string;
    from: 'user' | 'ai' | 'stepper';
    theme: 'light' | 'dark';
    imageDataUri?: string;
    showLoadingIndicator?: boolean;
    loadingPhase?: 'intro' | 'main' | 'completed' | 'error';
    mainAPIInProgress?: boolean;
    // ENHANCEMENT: Intro message support for code generation
    showIntroMessage?: boolean;
    // ENHANCEMENT: Typewriter effect support
    isTyping?: boolean;
    // ENHANCEMENT: Generation result support
    isGenerationResult?: boolean;
    generationResult?: GenerationResult;
    // ENHANCEMENT: Stepper support
    isStepper?: boolean;
  }[] = [];

  // ENHANCEMENT: Separate array for generation results as individual cards
  private _generationResults: GenerationResult[] = [];
  // Auto-scroll functionality removed

  // ENHANCEMENT: Prompt bar loading state management
  private promptBarLoadingStateSubject = new BehaviorSubject<PromptBarLoadingState>({
    isVisible: false,
    phase: null,
    status: null,
    loadingText: '',
    isInitialGeneration: false,
    isRegeneration: false
  });
  public readonly promptBarLoadingState$ = this.promptBarLoadingStateSubject.asObservable();

  // Getter for current loading state
  get currentPromptBarLoadingState(): PromptBarLoadingState {
    return this.promptBarLoadingStateSubject.value;
  }

  /**
   * Get dynamic loading text based on progress phase and status
   * @param phase The current progress phase
   * @param status The current status
   * @returns The appropriate loading text
   */
  private getLoadingTextForProgress(
    phase: 'CODE_GENERATION' | 'BUILD' | 'DEPLOY' | null,
    status: 'IN_PROGRESS' | 'COMPLETED' | null
  ): string {
    if (!phase || status !== 'IN_PROGRESS') {
      return '';
    }

    switch (phase) {
      case 'CODE_GENERATION':
        return 'Generating...';
      case 'BUILD':
        return 'Building...';
      case 'DEPLOY':
        return 'Deploying...';
      default:
        return 'Processing...';
    }
  }

  /**
   * Update prompt bar loading state
   * @param updates Partial updates to apply to the loading state
   */
  private updatePromptBarLoadingState(updates: Partial<PromptBarLoadingState>): void {
    const currentState = this.promptBarLoadingStateSubject.value;
    const newState = { ...currentState, ...updates };
    this.promptBarLoadingStateSubject.next(newState);
    this.logger.info('🔄 Prompt bar loading state updated:', newState);
  }

  /**
   * Show prompt bar loading indicator
   * @param phase The progress phase
   * @param status The current status
   * @param isInitialGeneration Whether this is initial generation
   * @param isRegeneration Whether this is regeneration
   */
  private showPromptBarLoading(
    phase: 'CODE_GENERATION' | 'BUILD' | 'DEPLOY',
    status: 'IN_PROGRESS' | 'COMPLETED',
    isInitialGeneration: boolean = false,
    isRegeneration: boolean = false
  ): void {
    const loadingText = this.getLoadingTextForProgress(phase, status);
    const shouldShow = status === 'IN_PROGRESS' && loadingText !== '';

    this.updatePromptBarLoadingState({
      isVisible: shouldShow,
      phase,
      status,
      loadingText,
      isInitialGeneration,
      isRegeneration
    });
  }

  /**
   * Hide prompt bar loading indicator
   */
  private hidePromptBarLoading(): void {
    this.updatePromptBarLoadingState({
      isVisible: false,
      phase: null,
      status: null,
      loadingText: '',
      isInitialGeneration: false,
      isRegeneration: false
    });
  }

  /**
   * ENHANCED: Public method to hide prompt bar loading indicator
   * Used by regeneration integration service for retry functionality
   */
  public hidePromptBarLoadingPublic(): void {
    this.hidePromptBarLoading();
  }

  /**
   * ENHANCED: Set code generation complete state
   * Re-enables prompt bar for retry functionality
   */
  public setCodeGenerationComplete(isComplete: boolean): void {
    this.isCodeGenerationComplete = isComplete;
    this.logger.info('🔄 Code generation complete state updated:', { isComplete });
    this.cdr.detectChanges();
  }

  /**
   * ENHANCED: Append error message to prompt bar
   * Used for retry functionality to append error with "fix it"
   */
  public appendErrorToPrompt(errorMessage: string): void {
    const retryPrompt = `${errorMessage} - fix it`;
    this.textValue = retryPrompt;
    this.currentPrompt = retryPrompt;
    this.logger.info('🔄 Error message appended to prompt bar:', {
      retryPromptLength: retryPrompt.length,
      retryPromptPreview: retryPrompt.substring(0, 100) + '...'
    });
    this.cdr.detectChanges();
  }

  /**
   * ENHANCED: Set text value directly
   * Fallback method for setting prompt text
   */
  public setTextValue(text: string): void {
    this.textValue = text;
    this.currentPrompt = text;
    this.logger.info('🔄 Text value set directly:', {
      textLength: text.length,
      textPreview: text.substring(0, 100) + '...'
    });
    this.cdr.detectChanges();
  }

  /**
   * Check if prompt bar loading indicator should be shown
   * @returns true if loading indicator should be visible
   */
  shouldShowPromptBarLoadingIndicator(): boolean {
    return this.currentPromptBarLoadingState.isVisible;
  }

  /**
   * Adds the selected image to the last user message in the chat
   * If no user message exists, it creates a new one
   */
  private addImageToLastUserMessage(): void {
    if (!this._selectedImageDataUri) return;

    // Find the last user message
    const lastUserMessageIndex = [...this._chatMessages].reverse().findIndex(msg => msg.from === 'user');

    if (lastUserMessageIndex >= 0) {
      // Convert reverse index to actual index
      const actualIndex = this._chatMessages.length - 1 - lastUserMessageIndex;
      // Update the last user message to include the image
      this._chatMessages[actualIndex].imageDataUri = this._selectedImageDataUri;
    } else {
      // If no user message exists, create a new one with the image
      this._chatMessages.push({
        text: '',
        from: 'user',
        theme: this.theme,
        imageDataUri: this._selectedImageDataUri
      });
    }

    // Auto-scroll removed - let user control scrolling
  }

  /**
   * Updates the theme of all messages to match the component's theme
   * Note: We're now using the component's theme directly in the template,
   * but we'll keep this method for backward compatibility
   */
  private updateMessagesTheme(): void {
    if (this._chatMessages && this._chatMessages.length > 0) {
      this._chatMessages.forEach(message => {
        message.theme = this._theme;
      });
    }

    // Force change detection to update the view
    this.cdr.detectChanges();
  }

  @Input()
  get chatMessages(): {
    id?: string;
    text: string;
    from: 'user' | 'ai' | 'stepper';
    theme: 'light' | 'dark';
    imageDataUri?: string;
    showLoadingIndicator?: boolean;
    loadingPhase?: 'intro' | 'main' | 'completed' | 'error';
    mainAPIInProgress?: boolean;
    // ENHANCEMENT: Intro message support for code generation
    showIntroMessage?: boolean;
    // ENHANCEMENT: Typewriter effect support
    isTyping?: boolean;
    // ENHANCEMENT: Generation result support
    isGenerationResult?: boolean;
    generationResult?: GenerationResult;
    // ENHANCEMENT: Stepper support
    isStepper?: boolean;
  }[] {
    return this._chatMessages;
  }

  // ENHANCEMENT: Getter for generation results
  get generationResults(): GenerationResult[] {
    return this._generationResults;
  }
  set chatMessages(value: {
    id?: string;
    text: string;
    from: 'user' | 'ai' | 'stepper';
    theme: 'light' | 'dark';
    imageDataUri?: string;
    showLoadingIndicator?: boolean;
    loadingPhase?: 'intro' | 'main' | 'completed' | 'error';
    mainAPIInProgress?: boolean;
    // ENHANCEMENT: Intro message support for code generation
    showIntroMessage?: boolean;
    // ENHANCEMENT: Typewriter effect support
    isTyping?: boolean;
    // ENHANCEMENT: Generation result support
    isGenerationResult?: boolean;
    generationResult?: GenerationResult;
    // ENHANCEMENT: Stepper support
    isStepper?: boolean;
  }[]) {
    // Check if messages have been added or changed
    const messagesAdded = value && this._chatMessages && value.length !== this._chatMessages.length;

    // Check if the last message content has changed (for streaming responses)
    let lastMessageChanged = false;
    if (value && this._chatMessages && value.length > 0 && this._chatMessages.length > 0) {
      const newLastMessage = value[value.length - 1];
      const oldLastMessage = this._chatMessages[this._chatMessages.length - 1];
      if (
        newLastMessage &&
        oldLastMessage &&
        newLastMessage.from === oldLastMessage.from &&
        newLastMessage.text !== oldLastMessage.text
      ) {
        lastMessageChanged = true;
      }
    }

    // Auto-scroll removed - let user control scrolling

    // Ensure each message has the correct theme
    if (value) {
      value.forEach(message => {
        if (!message.theme) {
          message.theme = this.theme;
        }
      });
    }

    this._chatMessages = value;



    // If new messages were added or content changed, force update the theme on all cards
    if (messagesAdded || lastMessageChanged) {
      // Use a shorter timeout for better responsiveness
      setTimeout(() => {
        this.forceUpdateCardThemes();
        // Auto-scroll removed - let user control scrolling
      }, 10); // Shorter delay for better responsiveness
    }
  }

  private _textValue: string = '';
  @Input()
  get textValue(): string {
    return this._textValue;
  }
  set textValue(value: string) {
    this._textValue = value;
    this.currentPrompt = value; // Sync with currentPrompt for enhancement
    this.textValueChange.emit(this._textValue);
    // Adjust textarea height when content changes
    this.adjustTextareaHeight();
  }

  @Output() textValueChange = new EventEmitter<string>();
  @Output() enterPressed = new EventEmitter<void>();
  @Output() iconClicked = new EventEmitter<{
    name: string;
    side: string;
    index: number;
    theme: string;
  }>();
  @Output() imageTransferred = new EventEmitter<void>();
  @Output() retryStep = new EventEmitter<number>();
  @Output() regenerationPayload = new EventEmitter<{ prompt: string; image: string[] }>();
  @Output() userMessageData = new EventEmitter<{
    prompt: string;
    images: Array<{ url: string; name: string; id: string }>;
    timestamp: string;
  }>();

  // Force inject services for command processing
  private codeGenerationIntroService = inject(CodeGenerationIntroService);
  private uiDesignIntroService = inject(UIDesignIntroService);
  private wireframeMockDataService = inject(WireframeMockDataService);

  @ViewChild('chatScrollContainer') private chatScrollContainer!: ElementRef;

  // ENHANCEMENT: Smart scroll navigation properties using Angular 19+ Signals
  // private scrollThreshold = 50; // pixels from top/bottom to consider "at edge"
  // private scrollAnimationDuration = 400; // milliseconds for smooth scroll

  // // Scroll navigation state using signals
  // private isAtTopSignal = signal(false);
  // private isAtBottomSignal = signal(true); // Start at bottom by default
  // private canScrollSignal = signal(false);

  // // Computed scroll navigation state
  // public readonly scrollNavigationState = computed<ScrollNavigationState>(() => ({
  //   isAtTop: this.isAtTopSignal(),
  //   isAtBottom: this.isAtBottomSignal(),
  //   showScrollButton: this.canScrollSignal() && !(this.isAtTopSignal() && this.isAtBottomSignal()),
  //   scrollDirection: this.isAtBottomSignal() ? 'up' : 'down',
  //   canScroll: this.canScrollSignal()
  // }));

  handleIconClick(event: { name: string; side: string; index: number }): void {
    // If code generation is not complete, don't allow icon clicks
    if (!this.isCodeGenerationComplete) {
      return;
    }

    this.iconClicked.emit({ ...event, theme: this.theme });
  }

  /**
   * Handle file attach icon click (simple icon, no pill)
   */
  handleFileAttach(): void {
    if (this.isEnhancing || this.isFileAttachDisabled || this.isCodeGenerationLoading) {
      return;
    }
    this.handleEnhancedAlternate();
  }

  /**
   * Check if user can send request (validates intent and generation state)
   */
  canSendRequest(): boolean {
    // Don't allow sending during generation/loading
    if (this.isCodeGenerationLoading || this.isEnhancing) {
      return false;
    }

    // Don't allow sending if code generation is not complete
    if (!this.isCodeGenerationComplete) {
      return false;
    }

    // Don't allow sending if no text
    if (!this.textValue || this.textValue.trim() === '') {
      return false;
    }

    // Don't allow sending if prompt is enhanced but intent is "no"
    if (this.isPromptEnhanced && !this.isEnhancedPromptValid) {
      return false;
    }

    return true;
  }

  /**
   * Handle enhanced send (exact same as prompt-content with payload support)
   */
  handleEnhancedSend(): void {
    if (!this.canSendRequest()) {
      if (this.isPromptEnhanced && !this.isEnhancedPromptValid) {
        this.toastService.warning('Please enhance the prompt again for better results before sending.');
      }
      return;
    }

    // FORCE INTERCEPT: Check for special commands before processing normally
    if (this.handleSpecialCommands(this.textValue)) {
      // Command was handled, clear the input and return
      this.clearAfterSubmission();
      return;
    }

    // Prepare submission data with image payload for regeneration
    this.prepareSubmissionData();

    // Emit user message data for chat cards display
    const userMessageData = this.getUserMessageData();
    this.userMessageData.emit(userMessageData);

    // Emit regeneration payload for parent components to use
    const payload = this.getRegenerationPayload();
    this.regenerationPayload.emit(payload);

    // Clear prompt and images after successful submission
    this.clearAfterSubmission();

    this.enterPressed.emit();
  }

  /**
   * Prepare submission data for regeneration with image payload
   */
  private prepareSubmissionData(): void {
    this.submissionData = {
      prompt: this.currentPrompt,
      timestamp: new Date().toISOString(),
      imageFile: this.selectedFile,
      imageUrl: this.selectedFiles.length > 0 ? this.selectedFiles[0].url : null,
      imageDataUri: this.selectedFiles.length > 0 ? this.selectedFiles[0].url : null,
      fileName: this.selectedFileName,
    };

    // Update prompt service with current data for regeneration
    this.promptService.setPrompt(this.currentPrompt);
    if (this.selectedFile) {
      this.promptService.setImage(this.selectedFile);
    }
  }

  /**
   * Get regeneration payload in the format: {"prompt": "", "image": []}
   */
  getRegenerationPayload(): { prompt: string; image: string[] } {
    const imageDataUris = this.getImageDataUris();
    return {
      prompt: this.currentPrompt || this.textValue || '',
      image: imageDataUris
    };
  }

  /**
   * Get icon color based on theme and state (exact same as prompt-content)
   */
  getIconColor(): string {
    return `var(--icon-enabled-color)`;
  }

  /**
   * Truncate file name for display (exact same as prompt-content)
   */
  truncateFileName(fileName: string): string {
    const maxLength = 20;
    if (fileName.length <= maxLength) return fileName;

    const extension = fileName.split('.').pop() || '';
    const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
    const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4);

    return `${truncatedName}...${extension}`;
  }

  /**
   * Show file preview (exact same as prompt-content)
   */
  showFilePreview(file: SelectedFile): void {
    this.previewImage = file;
    this.showPreview = true;
  }

  /**
   * Shows the image preview overlay
   * @param imageUrl URL of the image to preview
   * @param imageName Name of the image (optional)
   */
  showImagePreview(imageUrl: string, imageName: string = 'Image'): void {
    this.previewImage = { url: imageUrl, name: imageName };
    this.showPreview = true;
    // OnPush: Property changes trigger change detection automatically
  }

  /**
   * Closes the image preview overlay
   */
  closeImagePreview(): void {
    this.showPreview = false;
    this.previewImage = null;
    // OnPush: Property changes trigger change detection automatically
  }



  /**
   * Sanitizes message text for safe markdown rendering
   * @param text The message text to sanitize
   * @returns Sanitized text safe for markdown rendering
   */
  getSanitizedMessageText(text: string): string {
    return this.contentSanitizationService.sanitizeForMarkdown(text);
  }

  /**
   * Check if we should show the UI Design loading indicator
   * Returns the value of the isUIDesignLoading input property
   */
  shouldShowUIDesignLoadingIndicator(): boolean {
    return this.isUIDesignLoading;
  }

  /**
   * Check if we should show the Code Generation loading indicator
   * Returns true when standard code generation/regeneration is in progress (not UI Design mode)
   * ENHANCEMENT: Provides loading indicator for standard code generation and regeneration processes
   */
  shouldShowCodeGenerationLoadingIndicator(): boolean {
    return this.isCodeGenerationLoading;

  }

  /**
   * Check if regeneration progress description should be shown
   * ENHANCEMENT: Shows regeneration progress above prompt bar
   */
  shouldShowCodeRegenerationProgressDescription(): boolean {
    return !!(this.codeRegenerationProgressDescription &&
              this.codeRegenerationProgressDescription.trim() !== '' &&
              this.isCodeGenerationLoading);
  }



  /**
   * Handle step updates from the vertical stepper component
   * @param _stepIndex The index of the updated step (unused)
   */
  onStepUpdated(_stepIndex: number): void {
    // You can add additional logic here if needed
  }

  /**
   * Handle retry button click from the vertical stepper component
   * @param stepIndex The index of the step to retry
   */
  onRetryStep(stepIndex: number): void {

    // Change the status to IN_PROGRESS to show loading animation
    this.status = 'IN_PROGRESS';

    // Emit the retry event to the parent component
    this.retryStep.emit(stepIndex);

    // OnPush: Property changes and event emissions trigger change detection automatically
  }

  // Auto-scroll methods removed

  /**
   * Add stepper as a chat message for proper flow
   * This method should be called when stepper becomes active
   */
  addStepperMessage(): void {
    // Check if stepper message already exists
    const stepperExists = this._chatMessages.some(msg => msg.isStepper);
    if (!stepperExists) {
      this._chatMessages.push({
        text: '', // Empty text since stepper handles content
        from: 'stepper',
        theme: this.theme,
        isStepper: true
      });
    }
  }

  /**
   * Remove stepper message from chat flow
   * This method should be called when stepper is no longer needed
   */
  removeStepperMessage(): void {
    this._chatMessages = this._chatMessages.filter(msg => !msg.isStepper);
  }

  /**
   * Add a new standalone AI message with unique ID
   * FIXED: Creates isolated AI cards for intro messages
   */
  public addStandaloneAIMessage(text: string, messageId?: string): string {
    const uniqueId = messageId || this.generateUniqueMessageId();

    const newMessage = {
      id: uniqueId,
      text: text,
      from: 'ai' as const,
      theme: this.theme,
      showIntroMessage: true,
      isTyping: false
    };

    this._chatMessages.push(newMessage);

    // Force update the theme on the new card
    setTimeout(() => {
      this.forceUpdateCardThemes();
      // ENHANCEMENT: Update scroll state after message addition
      // this.updateScrollState();
    }, 10);

    this.logger.info('🆕 Added standalone AI message:', { messageId: uniqueId, textLength: text.length });
    return uniqueId;
  }

  /**
   * Add a new AI message with shimmer loading for intro/wireframe generation
   * Shows shimmer until API response arrives
   * CRITICAL: Allow shimmer loading during regeneration even if project was loaded from landing page
   */
  public addAIMessageWithShimmer(messageId?: string): string {
    // CRITICAL: Only prevent shimmer loading during initial project loading phase
    // Allow shimmer loading during regeneration even if project was loaded from landing page
    const isRegenerationActive = this.stepperStateService.isRegenerationActive();

    this.logger.info('🔍 Shimmer loading check:', {
      isProjectLoadingMode: this.isProjectLoadingMode,
      isRegenerationActive,
      shouldAllowShimmer: !this.isProjectLoadingMode || isRegenerationActive
    });

    if (this.isProjectLoadingMode && !isRegenerationActive) {
      this.logger.warn('⚠️ Preventing shimmer loading during initial project loading phase');
      return messageId || this.generateUniqueMessageId();
    }

    const uniqueId = messageId || this.generateUniqueMessageId();

    const newMessage = {
      id: uniqueId,
      text: '',
      from: 'ai' as const,
      theme: this.theme,
      showIntroMessage: true,
      showLoadingIndicator: true,
      loadingPhase: 'intro' as const,
      mainAPIInProgress: true,
      isTyping: false
    };

    this._chatMessages.push(newMessage);

    // Force update the theme on the new card
    setTimeout(() => {
      this.forceUpdateCardThemes();
      // ENHANCEMENT: Update scroll state after message addition
      // this.updateScrollState();
    }, 10);

    this.logger.info('🆕 Added AI message with shimmer loading:', { messageId: uniqueId });
    return uniqueId;
  }

  /**
   * Update an existing AI message with content and remove shimmer
   */
  public updateAIMessageContent(messageId: string, text: string): void {
    const messageIndex = this._chatMessages.findIndex(msg => msg.id === messageId);
    if (messageIndex !== -1) {
      this._chatMessages[messageIndex] = {
        ...this._chatMessages[messageIndex],
        text: text,
        showLoadingIndicator: false,
        loadingPhase: 'completed' as const,
        mainAPIInProgress: false
      };

      this.logger.info('✅ Updated AI message content:', { messageId, textLength: text.length });

      // Trigger change detection
      setTimeout(() => {

      }, 10);
    } else {
      this.logger.warn('⚠️ Could not find AI message to update:', messageId);
    }
  }

  /**
   * Update an existing AI message with content using fast typewriter effect
   */
  public updateAIMessageContentWithFastTypewriter(messageId: string, text: string): void {
    const messageIndex = this._chatMessages.findIndex(msg => msg.id === messageId);
    if (messageIndex !== -1) {
      // First, remove shimmer and set up for typewriter
      this._chatMessages[messageIndex] = {
        ...this._chatMessages[messageIndex],
        text: '',
        showLoadingIndicator: false,
        loadingPhase: 'completed' as const,
        mainAPIInProgress: false,
        isTyping: true
      };

      this.logger.info('✅ Starting fast typewriter effect for message:', { messageId, textLength: text.length });

      // Start fast typewriter effect
      this.startFastTypewriterEffect(messageIndex, text);
    } else {
      this.logger.warn('⚠️ Could not find AI message to update with typewriter:', messageId);
    }
  }

  /**
   * Start ultra-fast typewriter effect for generate application intro and regeneration
   */
  private startFastTypewriterEffect(messageIndex: number, fullText: string): void {
    const ULTRA_FAST_TYPING_SPEED = 3; // Ultra-fast 3ms per character for generate application
    let currentIndex = 0;

    const typeNextCharacter = () => {
      if (currentIndex < fullText.length && messageIndex < this._chatMessages.length) {
        currentIndex++;
        this._chatMessages[messageIndex].text = fullText.substring(0, currentIndex);
        this.cdr.detectChanges();

        // Continue typing
        setTimeout(typeNextCharacter, ULTRA_FAST_TYPING_SPEED);
      } else {
        // Typing complete
        if (messageIndex < this._chatMessages.length) {
          this._chatMessages[messageIndex].isTyping = false;
          this.cdr.detectChanges();

        }
      }
    };

    // Start typing
    typeNextCharacter();
  }

  /**
   * Update a specific message by ID
   * FIXED: Updates only the target message, not all messages
   */
  public updateMessageById(messageId: string, updates: Partial<any>): void {
    const messageIndex = this._chatMessages.findIndex(msg => msg.id === messageId);
    if (messageIndex !== -1) {
      this._chatMessages[messageIndex] = { ...this._chatMessages[messageIndex], ...updates };

      // Force update the theme on the updated card
      setTimeout(() => {
        this.forceUpdateCardThemes();
      }, 10);

      this.logger.debug('📝 Updated message by ID:', { messageId, updates });
    } else {
      this.logger.warn('⚠️ Message not found for update:', messageId);
    }
  }

  /**
   * Generate unique message ID
   */
  private generateUniqueMessageId(): string {
    return `msg-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Add generation result as a chat message for proper flow
   * This method can be called from parent components when code generation is completed
   */
  addGenerationResult(generationResult: GenerationResult): void {
    // Check for duplicate generation results to prevent duplicate accordions
    const existingResult = this._chatMessages.find(message =>
      message.isGenerationResult &&
      message.generationResult &&
      message.generationResult.version === generationResult.version &&
      message.generationResult.projectName === generationResult.projectName
    );

    if (existingResult) {
      this.logger.warn('⚠️ Duplicate generation result detected, skipping:', {
        version: generationResult.version,
        projectName: generationResult.projectName,
        existingTimestamp: existingResult.generationResult?.timestamp,
        newTimestamp: generationResult.timestamp,
        totalExistingMessages: this._chatMessages.length,
        existingGenerationResults: this._chatMessages.filter(m => m.isGenerationResult).length
      });
      this.logger.debug('[VERSION ACCORDION DEBUG] Duplicate prevention triggered - skipping duplicate accordion creation');
      return;
    }

    this.logger.info('✅ Adding new generation result:', {
      version: generationResult.version,
      projectName: generationResult.projectName,
      timestamp: generationResult.timestamp,
      totalExistingMessages: this._chatMessages.length,
      existingGenerationResults: this._chatMessages.filter(m => m.isGenerationResult).length
    });

    // Mark all previous generation results as not latest
    this.markPreviousResultsAsNotLatest();

    // Mark the new result as latest
    generationResult.isLatest = true;

    // Add generation result as a special chat message to maintain proper flow
    this._chatMessages.push({
      text: '', // Empty text since accordion handles content
      from: 'ai',
      theme: this.theme,
      isGenerationResult: true,
      generationResult: generationResult
    });

    // Also keep in separate array for backward compatibility
    this._generationResults.push(generationResult);

    // Force change detection
    this.cdr.detectChanges();



    // Auto-scroll removed - let user control scrolling
  }

  /**
   * Mark all previous generation results as not latest
   */
  private markPreviousResultsAsNotLatest(): void {
    // Update all existing generation results in chat messages
    this._chatMessages.forEach(message => {
      if (message.isGenerationResult && message.generationResult) {
        message.generationResult.isLatest = false;
      }
    });

    // Update all existing generation results in separate array
    this._generationResults.forEach(result => {
      result.isLatest = false;
    });
  }



  ngOnInit(): void {
    // Initialize component without auto-scroll functionality
    setTimeout(() => {
      // Ensure messages have the correct theme
      this.updateMessagesTheme();
      // Force update the theme on all cards
      this.forceUpdateCardThemes();

    }, 0);

    // Subscribe to stepper reset events
    this.stepperStateService.resetStepper$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(shouldReset => {
        if (shouldReset) {
          this.resetStepperState();
        }
      });

    // ENHANCED: Setup regeneration integration
    this.setupRegenerationIntegration();
  }

  /**
   * Resets the stepper state
   * This is called when the stepper state service emits a reset event
   */
  private resetStepperState(): void {
    // Reset the stepper component if it exists
    if (this.stepperComponent) {
      this.stepperComponent.resetStepper();
    }

    // No need to remove stepper messages since stepper is now standalone

    // Reset the stepper inputs
    this._progress = '';
    this._progressDescription = '';
    this.status = 'PENDING';

    // OnPush: Property changes trigger change detection automatically
  }

  /**
   * Setup regeneration integration service subscriptions
   * ENHANCED: Perfect integration for regeneration flow with loading indicators
   */
  private setupRegenerationIntegration(): void {
    this.logger.info('🔗 Setting up regeneration integration in chat-window');

    // FIXED: Register this chat window component for intro message creation
    this.regenerationIntegrationService.setChatWindowComponent(this);

    // ENHANCED: Also register with sequential regeneration service for shimmer loading
    this.sequentialRegenerationService.setChatWindowComponent(this);

    // Subscribe to regeneration active state
    this.regenerationIntegrationService.regenerationActive$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(isActive => {
        this.logger.info('🔄 Regeneration active state changed:', { isActive });

        // Update loading indicators based on regeneration state
        if (isActive) {
          this.handleRegenerationStart();
        } else {
          this.handleRegenerationComplete();
        }
      });

    // Subscribe to UI state updates for loading indicators
    this.regenerationIntegrationService.uiStateUpdates$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(stateUpdate => {
        this.logger.info('🎛️ Handling UI state update in chat-window:', stateUpdate);
        this.handleRegenerationUIStateUpdate(stateUpdate);
      });

    // ENHANCED: Subscribe to initial generation progress events
    this.enhancedSSEService.currentProgress$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(progress => {
        this.logger.info('📊 Initial generation progress update:', progress);
        this.handleInitialGenerationProgress(progress);
      });

    // ENHANCED: Subscribe to initial generation status events
    this.enhancedSSEService.currentStatus$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(status => {
        this.logger.info('📈 Initial generation status update:', status);
        this.handleInitialGenerationStatus(status);
      });

    // ENHANCED: Subscribe to accordion creation events for error handling
    this.regenerationIntegrationService.accordionCreate$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(accordionInfo => {
        this.logger.info('📋 Handling accordion creation event:', accordionInfo);
        this.handleAccordionCreation(accordionInfo);
      });

    this.logger.info('✅ Regeneration integration setup complete in chat-window');
  }

  /**
   * Handle regeneration start
   */
  private handleRegenerationStart(): void {
    this.logger.info('🚀 Handling regeneration start in chat-window');

    // Show loading indicators
    this.isCodeGenerationLoading = true;
    this.codeRegenerationProgressDescription = 'Generating...';

    this.cdr.detectChanges();
  }

  /**
   * Handle regeneration complete
   */
  private handleRegenerationComplete(): void {
    this.logger.info('✅ Handling regeneration complete in chat-window');

    // Hide loading indicators
    this.isCodeGenerationLoading = false;
    this.codeRegenerationProgressDescription = '';

    this.cdr.detectChanges();
  }

  /**
   * Handle regeneration UI state updates
   * ENHANCED: Now also updates prompt bar loading state
   * ENHANCED: Now handles FAILED status with detailed error message extraction
   */
  private handleRegenerationUIStateUpdate(stateUpdate: any): void {
    this.logger.info('🎛️ Handling regeneration UI state update:', stateUpdate);

    // ENHANCED: Handle FAILED status or FAILED progress with detailed error message extraction
    if (stateUpdate.status === 'FAILED' || stateUpdate.progress === 'FAILED') {
      this.logger.warn('❌ Regeneration FAILED - extracting detailed error message and updating state', {
        status: stateUpdate.status,
        progress: stateUpdate.progress,
        hasErrorMessage: !!stateUpdate.errorMessage
      });

      // ENHANCED: Extract detailed error message using same logic as /build/error API endpoint
      const detailedErrorMessage = this.extractDetailedErrorMessage(stateUpdate);
      this.logger.info('🔍 Detailed error message extracted for regeneration FAILED state:', {
        errorMessageLength: detailedErrorMessage?.length || 0,
        errorMessagePreview: detailedErrorMessage?.substring(0, 100) + '...'
      });

      this.isCodeGenerationLoading = false;
      this.codeRegenerationProgressDescription = '';
      this.hidePromptBarLoading();
      this.cdr.detectChanges();
      return;
    }

    // Update loading indicators based on phase and status
    if (stateUpdate.phase === 'CODE_GENERATION') {
      if (stateUpdate.status === 'IN_PROGRESS') {
        this.isCodeGenerationLoading = true;
        this.codeRegenerationProgressDescription = 'Generating...';
        // ENHANCED: Show prompt bar loading for regeneration
        this.showPromptBarLoading('CODE_GENERATION', 'IN_PROGRESS', false, true);
      } else if (stateUpdate.status === 'COMPLETED') {
        this.codeRegenerationProgressDescription = 'Editing...';
      }
    } else if (stateUpdate.phase === 'BUILD') {
      if (stateUpdate.status === 'IN_PROGRESS') {
        this.codeRegenerationProgressDescription = 'Building...';
        // ENHANCED: Show prompt bar loading for regeneration
        this.showPromptBarLoading('BUILD', 'IN_PROGRESS', false, true);
      } else if (stateUpdate.status === 'COMPLETED') {
        this.codeRegenerationProgressDescription = 'Deploying...';
      }
    } else if (stateUpdate.phase === 'DEPLOY') {
      if (stateUpdate.status === 'IN_PROGRESS') {
        this.codeRegenerationProgressDescription = 'Deploying...';
        // ENHANCED: Show prompt bar loading for regeneration
        this.showPromptBarLoading('DEPLOY', 'IN_PROGRESS', false, true);
      } else if (stateUpdate.status === 'COMPLETED') {
        this.isCodeGenerationLoading = false;
        this.codeRegenerationProgressDescription = '';
        // ENHANCED: Hide prompt bar loading when regeneration is complete
        this.hidePromptBarLoading();
      }
    }

    this.cdr.detectChanges();
  }

  /**
   * Handle initial generation progress updates
   * ENHANCED: Updates prompt bar loading state for initial generation
   * FIXED: Now handles FAILED progress to hide loading text
   */
  private handleInitialGenerationProgress(progress: string | null): void {
    this.logger.info('📊 Handling initial generation progress:', progress);

    if (!progress) return;

    // FIXED: Handle FAILED progress immediately - hide loading
    if (progress === 'FAILED') {
      this.logger.warn('❌ Initial generation progress FAILED - hiding prompt bar loading');
      this.hidePromptBarLoading();
      return;
    }

    // Map progress to phase for prompt bar loading
    let phase: 'CODE_GENERATION' | 'BUILD' | 'DEPLOY' | null = null;

    if (progress === 'CODE_GENERATION' || progress === 'CODE_GENERATION_STARTED' || progress === 'CODE_GENERATION_SUCCEEDED') {
      phase = 'CODE_GENERATION';
    } else if (progress === 'BUILD' || progress === 'BUILD_STARTED' || progress === 'BUILD_SUCCEEDED') {
      phase = 'BUILD';
    } else if (progress === 'DEPLOY' || progress === 'DEPLOYED' || progress === 'deployed') {
      phase = 'DEPLOY';
    }

    if (phase) {
      // Subscribe to current status to determine if we should show loading
      this.enhancedSSEService.currentStatus$
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe(currentStatus => {
          if (currentStatus === 'IN_PROGRESS') {
            this.showPromptBarLoading(phase!, 'IN_PROGRESS', true, false);
          }
        });
    }
  }

  /**
   * Handle initial generation status updates
   * ENHANCED: Updates prompt bar loading state for initial generation
   * ENHANCED: Now handles FAILED status with detailed error message extraction
   */
  private handleInitialGenerationStatus(status: string | null): void {
    this.logger.info('📈 Handling initial generation status:', status);

    if (!status) return;

    // ENHANCED: Handle FAILED status with detailed error message extraction
    if (status === 'FAILED') {
      this.logger.warn('❌ Initial generation FAILED - extracting detailed error message and hiding prompt bar loading');

      // ENHANCED: Log that detailed error message extraction would be available
      // The actual error message will be handled by the accordion creation process
      this.logger.info('🔍 Initial generation FAILED - detailed error message will be extracted during accordion creation');

      this.hidePromptBarLoading();
      return;
    }

    // Subscribe to current progress to determine phase
    this.enhancedSSEService.currentProgress$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(currentProgress => {
        let phase: 'CODE_GENERATION' | 'BUILD' | 'DEPLOY' | null = null;

        // Map ProgressState values to our phase types
        if (currentProgress === 'BUILD' || currentProgress === 'BUILD_STARTED' || currentProgress === 'BUILD_SUCCEEDED') {
          phase = 'BUILD';
        } else if (currentProgress === 'DEPLOY' || currentProgress === 'DEPLOYED' || currentProgress === 'deployed') {
          phase = 'DEPLOY';
        }

        if (phase) {
          if (status === 'IN_PROGRESS') {
            this.showPromptBarLoading(phase, 'IN_PROGRESS', true, false);
          } else if (status === 'COMPLETED' && phase === 'DEPLOY') {
            // Hide loading when deployment is completed
            this.hidePromptBarLoading();
          }
        }
      });
  }

  /**
   * ENHANCED: Handle accordion creation events from regeneration integration service
   * Converts accordion info to GenerationResult and adds to chat messages
   * ENHANCED: Now uses same error message extraction logic as /build/error API endpoint
   */
  private handleAccordionCreation(accordionInfo: any): void {
    this.logger.info('📋 Processing accordion creation:', {
      hasFiles: !!accordionInfo.files,
      fileCount: accordionInfo.files?.length || 0,
      version: accordionInfo.version,
      projectName: accordionInfo.projectName,
      isError: accordionInfo.isError,
      hasErrorMessage: !!accordionInfo.errorMessage
    });

    // ENHANCED: Extract detailed error message using same logic as /build/error API endpoint
    let enhancedErrorMessage = accordionInfo.errorMessage;
    if (accordionInfo.isError && accordionInfo.errorMessage) {
      // Use the same error message extraction logic that's sent to /build/error API as user_request
      enhancedErrorMessage = this.extractDetailedErrorMessage(accordionInfo);
      this.logger.info('🔍 Enhanced error message extracted for display:', {
        originalLength: accordionInfo.errorMessage?.length || 0,
        enhancedLength: enhancedErrorMessage?.length || 0,
        enhancedPreview: enhancedErrorMessage?.substring(0, 100) + '...'
      });
    }

    // Convert accordion info to GenerationResult format
    const generationResult: GenerationResult = {
      type: accordionInfo.isError ? 'error' : 'success',
      version: accordionInfo.version || 1,
      projectName: accordionInfo.projectName || 'Generated Project',
      files: accordionInfo.files || [],
      timestamp: accordionInfo.timestamp || new Date(),
      isLatest: true, // New accordions are always latest
      // ENHANCED: Use enhanced error message that matches /build/error API endpoint format
      errorMessage: enhancedErrorMessage,
      projectId: accordionInfo.projectId,
      jobId: accordionInfo.jobId
    };

    // ENHANCED: Handle code tab state management during FAILED events
    if (accordionInfo.isError) {
      this.handleCodeTabStateForFailedGeneration(accordionInfo.projectId, accordionInfo.jobId);
    }

    // Add generation result to chat messages
    this.addGenerationResult(generationResult);

    this.logger.info('✅ Accordion added to chat messages:', {
      type: generationResult.type,
      version: generationResult.version,
      hasErrorMessage: !!generationResult.errorMessage,
      errorMessageLength: generationResult.errorMessage?.length || 0
    });
  }

  /**
   * ENHANCED: Extract detailed error message using same logic as /build/error API endpoint
   * This ensures consistency between UI display and API error reporting
   * CRITICAL: Uses same extraction logic as ErrorReportingService.reportBuildError user_request parameter
   */
  private extractDetailedErrorMessage(accordionInfo: any): string {
    // Use the same error message extraction logic as regeneration integration service
    // This matches the logic used in /build/error API endpoint user_request parameter

    // CRITICAL: First try to extract from log field (same as ErrorReportingService)
    if (accordionInfo.log && typeof accordionInfo.log === 'string') {
      try {
        // Try to parse JSON log
        const logData = JSON.parse(accordionInfo.log);
        if (logData.message) {
          this.logger.info('📋 Extracted detailed error message from parsed log data:', {
            messageLength: logData.message.length,
            messagePreview: logData.message.substring(0, 100) + '...'
          });
          return logData.message.trim();
        }
        // Return raw log if not JSON but has content
        return accordionInfo.log.trim();
      } catch {
        // Return raw log if JSON parsing fails
        this.logger.info('📋 Using raw log data as detailed error message');
        return accordionInfo.log.trim();
      }
    }

    // Try to extract from errorMessage field as fallback (same as ErrorReportingService)
    if (accordionInfo.errorMessage && typeof accordionInfo.errorMessage === 'string') {
      this.logger.info('📋 Using errorMessage field as detailed error message fallback');
      return accordionInfo.errorMessage.trim();
    }

    // Handle complete failure case (same as ErrorReportingService)
    if (accordionInfo.status === 'FAILED' && accordionInfo.progress === 'FAILED') {
      return 'Error with the application. You can retry to fix it.';
    }

    // Default error message based on progress phase (same as ErrorReportingService)
    const phase = accordionInfo.errorPhase || 'generation';
    return `${phase.toLowerCase().replace('_', ' ')} failed. Please try again.`;
  }

  /**
   * ENHANCED: Handle code tab state management during FAILED events
   * Enables code tab if code is available, keeps disabled if no code exists
   * CRITICAL: Checks code availability before enabling tab during error states
   */
  private handleCodeTabStateForFailedGeneration(projectId?: string, jobId?: string): void {
    this.logger.info('🔍 Handling code tab state for failed generation:', {
      hasProjectId: !!projectId,
      hasJobId: !!jobId
    });

    // Check if code is available from various sources
    const hasCodeFromFiles = this.checkCodeAvailabilityFromFiles();
    // const hasCodeFromMonaco = this.checkCodeAvailabilityFromMonaco();
    const hasCodeFromAnySource = hasCodeFromFiles;

    this.logger.info('📊 Code availability check results:', {
      hasCodeFromFiles,
      hasCodeFromAnySource,
      decision: hasCodeFromAnySource ? 'Enable code tab' : 'Keep code tab disabled'
    });

    // Enable code tab only if code is available
    if (hasCodeFromAnySource) {
      this.logger.info('✅ Code available during FAILED state - enabling code tab for user access');
      // Note: The actual code tab enabling will be handled by the code-window component
      // This method provides the logic for determining when it should be enabled
    } else {
      this.logger.info('❌ No code available during FAILED state - keeping code tab disabled');
    }
  }

  /**
   * ENHANCED: Check if code is available from file sources
   * Used to determine code tab state during FAILED events
   */
  private checkCodeAvailabilityFromFiles(): boolean {
    // Check if there are any generation results with files
    const hasFilesInResults = this._generationResults.some(result =>
      result.files && result.files.length > 0
    );

    // Check if there are any files in chat messages
    const hasFilesInMessages = this._chatMessages.some(message =>
      message.isGenerationResult &&
      message.generationResult?.files &&
      message.generationResult.files.length > 0
    );

    return hasFilesInResults || hasFilesInMessages;
  }

  /**
   * ENHANCED: Check if code is available from Monaco editor sources
   * Used to determine code tab state during FAILED events
   */
  // private checkCodeAvailabilityFromMonaco(): boolean {
  //   // This would typically check if Monaco editor has any content
  //   // For now, we'll return false as Monaco integration would need to be checked
  //   // in the code-window component where Monaco editor is actually located
  //   return false;
  // }

  // Auto-scroll mutation observer removed

  /**
   * Forces an update of the theme on all cards by directly manipulating the DOM
   * This is a fallback method to ensure the theme is applied correctly
   */
  private forceUpdateCardThemes(): void {
    setTimeout(() => {
      try {
        // Get all card elements within this component
        const cardElements = document.querySelectorAll('.chat-wrapper awe-cards .awe-card');

        // Apply the appropriate theme class to each card
        cardElements.forEach(card => {
          // Remove existing theme classes
          card.classList.remove('awe-card--light', 'awe-card--dark');

          // Add the current theme class
          card.classList.add(`awe-card--${this._theme}`);

          // Force the background color based on the card type and theme
          const isUserCard = card.closest('.user-card') !== null;
          const isAiCard = card.closest('.ai-card') !== null;

          if (this._theme === 'dark') {
            if (isUserCard) {
              (card as HTMLElement).style.backgroundColor = 'var(--color-background-dark)';
              (card as HTMLElement).style.color = 'var(--text-white)';
            } else if (isAiCard) {
              (card as HTMLElement).style.backgroundColor =
                'var(--color-background-secondary-light)';
              (card as HTMLElement).style.color = 'var(--text-white)';
            }
          } else {
            if (isUserCard) {
              (card as HTMLElement).style.backgroundColor =
                'var(--Neutral-N-100, var(--color-background-light))';
              (card as HTMLElement).style.color = 'var(--text-black)';
            } else if (isAiCard) {
              (card as HTMLElement).style.backgroundColor =
                'var(--Neutral-N-50, var(--color-background-light))';
              (card as HTMLElement).style.color = 'var(--text-black)';
            }
          }
        });
      } catch (error) {
        this.logger.error('Error updating card themes:', error);
      }
    }, 100); // Small delay to ensure DOM is ready
  }

  // Auto-scroll helper methods removed

  // Auto-scroll listener removed

  ngOnDestroy(): void {
    // Clear stepper timeouts
    this.clearAllTimeouts();
    // Auto-scroll cleanup removed

    // Note: Subscriptions are automatically cleaned up by takeUntilDestroyed
    // We can't easily remove the exact same scroll event listener we added,
    // but we've cleaned up the timeouts and observers which is the main concern for memory leaks
  }

  // We'll set up the scroll event listener in ngOnInit instead of using HostListener

  ngAfterViewChecked(): void {
    // Check if we need to update card themes (only do this occasionally to avoid performance issues)
    if (this.shouldUpdateCardThemes) {
      this.shouldUpdateCardThemes = false;
      this.forceUpdateCardThemes();
    }
    // Auto-scroll removed from view checked
  }

  // Auto-scroll methods removed

  // Flag to track if we need to update card themes
  private shouldUpdateCardThemes = false;

  // Auto-scroll method removed

  /**
   * Update stepper state - stepper is now integrated in chat flow
   * This method is called when the stepper state changes
   */
  updateStepperMessage(): void {
    // Ensure stepper message exists in chat flow when stepper is active
    if (this.showStepper) {
      this.addStepperMessage();
    }
    // Trigger change detection to update the inline stepper
    this.cdr.detectChanges();
  }

  // No longer need stepper-related methods as we're using the VerticalStepperComponent

  /**
   * Clear all timeouts
   */
  private clearAllTimeouts(): void {
    Object.values(this.timeoutRefs).forEach(timeoutId => clearTimeout(timeoutId));
    this.timeoutRefs = {};
  }





  /**
   * Handle file upload functionality (exact same as prompt-content)
   */
  private handleEnhancedAlternate(): void {
    if (this.isEnhancing || this.isCodeGenerationLoading) {
      return;
    }
    if (this.isFileAttachDisabled) {
      this.fileError = `Only 1 image can be uploaded at a time`;
      return;
    }
    const fileInput = this.createFileInput();
    fileInput.addEventListener('change', (event: Event) => this.handleFileSelect(event));
    fileInput.click();
  }

  /**
   * Create file input element (exact same as prompt-content)
   */
  private createFileInput(): HTMLInputElement {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'image/*';
    fileInput.style.display = 'none';
    document.body.appendChild(fileInput);
    return fileInput;
  }

  /**
   * Handle file selection (exact same as prompt-content)
   */
  private handleFileSelect(event: Event): void {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];
    if (!file || !this.validateFile(file)) return;

    // Process file immediately for fast UI response
    this.processFileImmediately(file);

    document.body.removeChild(event.target as HTMLElement);
  }

  /**
   * Validate selected file (exact same as prompt-content)
   */
  private validateFile(file: File): boolean {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];

    if (!allowedTypes.includes(file.type)) {
      this.toastService.error('Please select a valid image file (JPEG, PNG, GIF, WebP)');
      return false;
    }

    if (file.size > maxSize) {
      this.toastService.error('File size must be less than 10MB');
      return false;
    }

    return true;
  }

  /**
   * Process file immediately for fast UI response (exact same as prompt-content)
   */
  private processFileImmediately(file: File): void {
    // Reset enhancement state
    this.promptService.resetEnhancedPromptState();
    this.isPromptEnhanced = false;
    this.isEnhancedPromptValid = true;
    this.enhanceClickCount = 0;

    // Create file URL immediately for instant preview
    const fileUrl = URL.createObjectURL(file);
    const newFile: SelectedFile = {
      id: Math.random().toString(36).substring(2, 11),
      name: file.name || `pasted-${Date.now()}.png`,
      url: fileUrl,
      type: file.type,
    };

    // Update UI immediately
    this.selectedFile = file;
    this.selectedFileName = newFile.name;
    this.selectedFiles = [newFile];
    this.updateFileAttachPillStatus();
    this.updateSendButtonState();

    // Force immediate UI update to show file preview
    this.cdr.detectChanges();

    // Update services
    this.promptService.setPrompt(this.textValue);
    this.promptService.setImage(file);

    // Process file data in background (slow part)
    const reader = new FileReader();
    reader.onload = e => this.handleFileReaderLoad(e, file, newFile, fileUrl);
    reader.readAsDataURL(file);

    // When image is uploaded, automatically append text instead of just changing placeholder
    if (!this.textValue || this.textValue.trim() === '') {
      // Automatically append a starter text for image-based prompts
      const imagePromptStarter = 'Create a website based on this image';
      this.textValue = imagePromptStarter;

      // Force UI update
      this.cdr.detectChanges();
    }

    // Always update the service with current prompt after processing
    this.promptService.setPrompt(this.textValue);
  }

  /**
   * Handle file reader load event (exact same as prompt-content)
   */
  private handleFileReaderLoad(e: ProgressEvent<FileReader>, file: File, newFile: SelectedFile, fileUrl: string): void {
    const imageDataUri = e.target?.result as string;

    // Update submission data with image information
    this.submissionData = {
      ...this.submissionData,
      prompt: this.currentPrompt,
      imageFile: file,
      imageUrl: fileUrl,
      imageDataUri,
      fileName: newFile.name,
    };

    // Reset enhancement state when new image is uploaded
    this.promptService.resetEnhancedPromptState();
    this.isPromptEnhanced = false;
    this.isEnhancedPromptValid = true;
    this.enhanceClickCount = 0;

    // Update the file URL with the data URI for better performance
    newFile.url = imageDataUri;

    // Update prompt service
    this.promptService.setPrompt(this.currentPrompt);
    this.promptService.setImage(file);

    // Clean up object URL
    URL.revokeObjectURL(fileUrl);
  }

  /**
   * Update file attach pill status (exact same as prompt-content)
   */
  private updateFileAttachPillStatus(): void {
    this.isFileAttachDisabled = this.selectedFiles.length >= this.maxAllowedFiles;
  }

  /**
   * Update send button state (exact same as prompt-content)
   */
  private updateSendButtonState(): void {
    // Implementation for updating send button state
    this.cdr.detectChanges();
  }

  /**
   * Handle enhance text functionality (exact same as prompt-content)
   */
  handleEnhanceText(): void {
    if (this.enhanceClickCount >= this.maxEnhanceClicks ||
        !this.currentPrompt?.trim() ||
        this.isCodeGenerationLoading) {
      return;
    }

    this.isEnhancing = true;

    const imageDataUris = this.selectedFiles.length && this.submissionData.imageDataUri
      ? [this.submissionData.imageDataUri]
      : [];

    // Determine the correct project type based on current mode
    const projectType = this.isUIDesignMode ? 'Generate Wireframes' : 'Generate Application';

    this.promptService.enhancePrompt(this.currentPrompt, projectType, imageDataUris)
      .subscribe({
        next: (response: any) => {
          let parsedResponse: any;
          try {
            // Handle response that might be wrapped in markdown code blocks
            let responseText = typeof response === 'string' ? response : JSON.stringify(response);

            // Check if response is wrapped in markdown code blocks
            const codeBlockMatch = responseText.match(/```(?:json)?\s*\n?([\s\S]*?)\n?```/);
            if (codeBlockMatch) {
              responseText = codeBlockMatch[1].trim();
            }

            parsedResponse = JSON.parse(responseText);
          } catch (error) {
            // If parsing fails, try to use the response as-is
            parsedResponse = typeof response === 'object' ? response : { prompt: response };
          }

          const { prompt, intent } = parsedResponse || {};
          if (!prompt) {
            return;
          }

          this.currentPrompt = prompt;
          this.textValue = prompt; // Update the text value
          this.promptService.setEnhancedPrompt(prompt);
          this.isPromptEnhanced = true;

          const normalizedIntent = intent?.toLowerCase() || '';
          if (normalizedIntent === 'yes') {
            this.isEnhancedPromptValid = true;
            this.enhanceClickCount++;
            this.toastService.success('Prompt enhanced successfully. Ready to generate code.');
          } else {
            this.isEnhancedPromptValid = false;
            this.toastService.warning('Prompt needs more details. Click enhance again for better results.');
          }
        },
        error: () => {
          this.isEnhancedPromptValid = true;
          this.toastService.error('Unable to enhance prompt. Please try again with more specific details.');
        },
        complete: () => {
          this.isEnhancing = false;
        }
      });
  }



  /**
   * Remove uploaded file (exact same as prompt-content)
   */
  removeFile(fileId: string): void {
    this.selectedFiles = this.selectedFiles.filter(file => file.id !== fileId);

    if (this.selectedFiles.length === 0) {
      this.selectedFile = null;
      this.selectedFileName = '';
      this.isFileAttachDisabled = false;
      this.enhanceClickCount = 0;

      // Clear submission data
      this.submissionData = {
        prompt: this.currentPrompt,
        timestamp: new Date().toISOString(),
        imageFile: null,
        imageUrl: null,
        imageDataUri: null,
        fileName: null,
      };

      // Clear from prompt service
      this.promptService.setImage(null);
    }

    this.updateFileAttachPillStatus();
    this.updateSendButtonState();
    this.cdr.detectChanges();
  }

  /**
   * Get current submission data for regeneration payload
   */
  getSubmissionData(): any {
    return {
      prompt: this.currentPrompt,
      timestamp: this.submissionData.timestamp,
      imageFile: this.submissionData.imageFile,
      imageUrl: this.submissionData.imageUrl,
      imageDataUri: this.submissionData.imageDataUri,
      fileName: this.submissionData.fileName,
      isPromptEnhanced: this.isPromptEnhanced,
      isEnhancedPromptValid: this.isEnhancedPromptValid,
    };
  }

  /**
   * Get image data URIs for regeneration
   */
  getImageDataUris(): string[] {
    return this.submissionData.imageDataUri ? [this.submissionData.imageDataUri] : [];
  }

  /**
   * Get user message data with images for chat cards
   */
  getUserMessageData(): {
    prompt: string;
    images: Array<{
      url: string;
      name: string;
      id: string;
    }>;
    timestamp: string;
  } {
    return {
      prompt: this.currentPrompt || this.textValue || '',
      images: this.selectedFiles.map(file => ({
        url: file.url,
        name: file.name,
        id: file.id
      })),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Clear images after successful submission
   * This ensures proper state cleanup and prevents image data from persisting
   */
  private clearImagesAfterSubmission(): void {
    // Clear selected files array
    this.selectedFiles = [];

    // Clear individual file references
    this.selectedFile = null;
    this.selectedFileName = '';

    // Clear submission data
    this.submissionData = {
      ...this.submissionData,
      imageFile: null,
      imageUrl: null,
      imageDataUri: null,
      fileName: null,
    };

    // Clear image from prompt service
    this.promptService.setImage(null);

    // Update file attach pill status
    this.updateFileAttachPillStatus();

    // Trigger change detection to update UI
    this.cdr.detectChanges();

    this.logger?.info('🧹 Images cleared after successful submission');
  }

  /**
   * Clear current prompt and images after successful submission
   */
  clearAfterSubmission(): void {
    this.textValue = '';
    this.currentPrompt = '';
    this.selectedFiles = [];
    this.selectedFile = null;
    this.selectedFileName = '';
    this.isFileAttachDisabled = false;
    this.isPromptEnhanced = false;
    this.isEnhancedPromptValid = true;
    this.enhanceClickCount = 0;

    // Reset textarea height after clearing content
    this.adjustTextareaHeight();

    // Clear submission data
    this.submissionData = {
      prompt: '',
      timestamp: new Date().toISOString(),
      imageFile: null,
      imageUrl: null,
      imageDataUri: null,
      fileName: null,
    };

    // Clear from prompt service
    this.promptService.setPrompt('');
    this.promptService.setImage(null);
  }

  /**
   * FORCE IMPLEMENTATION: Handle special commands like /intro and /wireframe-generation
   * @param prompt - The user input prompt
   * @returns true if a special command was handled, false otherwise
   */
  private handleSpecialCommands(prompt: string): boolean {
    const trimmedPrompt = prompt.trim().toLowerCase();

    if (trimmedPrompt.startsWith('/intro')) {
      this.handleIntroCommand(prompt);
      return true;
    }

    if (trimmedPrompt.startsWith('/wireframe-generation')) {
      this.handleWireframeGenerationCommand(prompt);
      return true;
    }

    return false;
  }

  /**
   * FORCE IMPLEMENTATION: Handle /intro command - creates intro message with shimmer loading
   */
  private handleIntroCommand(prompt: string): void {
    this.logger.info('🎭 FORCE: Handling /intro command in chat-window');

    // Extract the actual request after the command
    const userRequest = prompt.replace(/^\/intro\s*/i, '').trim() || 'Generate an intro message';

    // Add user message to chat immediately
    this._chatMessages.push({
      id: this.generateUniqueMessageId(),
      text: prompt,
      from: 'user',
      theme: this.theme
    });

    // Force trigger change detection
    this.cdr.detectChanges();

    // Use the code generation intro service with shimmer loading
    this.codeGenerationIntroService.createAndDisplayIntroCardWithShimmer(
      userRequest,
      [], // No code files for intro command
      this // Pass this chat-window component
    ).subscribe({
      next: (messageId) => {
        this.logger.info('✅ FORCE: Intro command completed successfully:', messageId);
      },
      error: (error) => {
        this.logger.error('❌ FORCE: Intro command failed:', error);
        this.toastService.error('Failed to generate intro message. Please try again.');
      }
    });
  }

  /**
   * FORCE IMPLEMENTATION: Handle /wireframe-generation command - creates wireframe intro with shimmer loading
   */
  private handleWireframeGenerationCommand(prompt: string): void {
    this.logger.info('🎯 FORCE: Handling /wireframe-generation command in chat-window');

    // Extract the actual request after the command
    const userRequest = prompt.replace(/^\/wireframe-generation\s*/i, '').trim() || 'Generate a wireframe';

    // Add user message to chat immediately
    this._chatMessages.push({
      id: this.generateUniqueMessageId(),
      text: prompt,
      from: 'user',
      theme: this.theme
    });

    // Force trigger change detection
    this.cdr.detectChanges();

    // Start wireframe stepper functionality
    this.startWireframeStepperProcess();

    // Use the UI design intro service with shimmer loading
    this.uiDesignIntroService.createAndDisplayIntroCardWithShimmer(
      userRequest,
      this // Pass this chat-window component
    ).subscribe({
      next: (messageId) => {
        this.logger.info('✅ FORCE: Wireframe generation command completed successfully:', messageId);
        // Store the message ID for stepper integration
        this.wireframeStepperMessageId.set(messageId);
      },
      error: (error) => {
        this.logger.error('❌ FORCE: Wireframe generation command failed:', error);
        this.toastService.error('Failed to Generate Wireframes intro message. Please try again.');

        // Handle wireframe generation error
        this.wireframeMockDataService.handleGenerationError(error);
        this.wireframeStepperVisible.set(false);
        this.wireframeCurrentStep.set('');
      }
    });
  }

  /**
   * Start wireframe stepper process with mock data integration
   */
  private startWireframeStepperProcess(): void {
    this.logger.info('🎯 Starting wireframe stepper process');

    // Show wireframe stepper
    this.wireframeStepperVisible.set(true);

    // Subscribe to wireframe generation progress
    this.wireframeMockDataService.generationProgress$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(progress => {
        if (progress.currentStep < progress.steps.length) {
          const currentStep = progress.steps[progress.currentStep];
          this.wireframeCurrentStep.set(currentStep.title);
          this.updateWireframeStepperInChat(currentStep.title);
        }

        if (progress.isComplete) {
          this.wireframeStepperVisible.set(false);
          this.wireframeCurrentStep.set('');
        }
      });
  }

  /**
   * Update wireframe stepper display in chat messages
   */
  private updateWireframeStepperInChat(stepTitle: string): void {
    const messageId = this.wireframeStepperMessageId();
    if (!messageId) return;

    // Find the message and update its content with current step
    const messageIndex = this._chatMessages.findIndex(msg => msg.id === messageId);
    if (messageIndex !== -1) {
      const message = this._chatMessages[messageIndex];

      // Update the message text with current step information
      const baseText = message.text.split('\n\n**Current Step:**')[0]; // Keep original text
      message.text = `${baseText}\n\n**Current Step:** ${stepTitle}`;

      // Ensure the message shows as active
      message.showLoadingIndicator = true;
      message.loadingPhase = 'main';

      this.cdr.detectChanges();
    }
  }

  /**
   * Clear all chat messages and reset conversation state
   * Used when navigating back to home screen
   */
  clearAllMessages(): void {
    this._chatMessages = [];
    this._generationResults = [];
    this.clearAfterSubmission();

    // WIREFRAME: Reset wireframe generation state
    this.wireframeStepperVisible.set(false);
    this.wireframeCurrentStep.set('');
    this.wireframeStepperMessageId.set(null);

    // Stop wireframe mock data generation if active
    if (this.wireframeMockDataService.isActive()) {
      this.wireframeMockDataService.resetGenerationState();
    }

    this.cdr.detectChanges();
  }

  // No longer need these methods as we're using the VerticalStepperComponent

  /**
   * Adjust textarea height based on content
   * Ensures proper height reset after large content is entered
   */
  private adjustTextareaHeight(): void {
    setTimeout(() => {
      const textAreas = document.querySelectorAll<HTMLTextAreaElement>('.prompt-text');
      if (!textAreas.length) return;

      let maxHeight = 0;
      textAreas.forEach(textArea => {
        textArea.style.height = 'auto';
        maxHeight = Math.max(maxHeight, textArea.scrollHeight);
      });

      // Reset height if content is empty, otherwise set to content height
      const shouldApplyHeight = !!this._textValue?.trim();
      const heightValue = shouldApplyHeight ? `${maxHeight}px` : '';

      textAreas.forEach(textArea => {
        textArea.style.height = heightValue;
      });

      this.cdr.detectChanges();
    }, 0);
  }

}
